/* Custom styles for Todo App */

:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-bg: #f8f9fa;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.todo-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

.todo-item {
    background: white;
    border-radius: 15px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    padding: 25px;
    transition: all 0.3s ease;
    border-left: 5px solid var(--primary-color);
}

.todo-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.todo-item.todo-completed {
    opacity: 0.7;
    border-left-color: var(--success-color);
}

.todo-completed .todo-title {
    text-decoration: line-through;
    color: #6c757d;
}

.btn-circle {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid;
    transition: all 0.3s ease;
}

.btn-circle:hover {
    transform: scale(1.1);
}

.add-todo-form {
    background: white;
    border-radius: 15px;
    box-shadow: var(--shadow);
    padding: 30px;
    margin-bottom: 30px;
    border-top: 5px solid var(--success-color);
}

.stats-card {
    background: white;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.navbar {
    box-shadow: var(--shadow);
}

.alert {
    border-radius: 10px;
    border: none;
    box-shadow: var(--shadow);
}

.card {
    border-radius: 15px;
    box-shadow: var(--shadow);
    border: none;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.btn {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* Animation for new todos */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.todo-item {
    animation: slideIn 0.5s ease;
}

/* Responsive design */
@media (max-width: 768px) {
    .todo-container {
        padding: 10px;
    }
    
    .todo-item {
        padding: 20px;
    }
    
    .add-todo-form {
        padding: 20px;
    }
    
    .btn-circle {
        width: 40px;
        height: 40px;
    }
}
