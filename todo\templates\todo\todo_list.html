﻿{% extends 'todo/base.html' %} {% block title %}Todo List{% endblock %} {% block
content %}
<div class="container mt-4">
  <h1 class="text-center mb-4">My Todo List</h1>

  <div class="card mb-4">
    <div class="card-header">
      <h3>Add New Todo</h3>
    </div>
    <div class="card-body">
      <form method="post" action="{% url 'add_todo' %}">
        {% csrf_token %}
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="title" class="form-label">Title *</label>
            <input
              type="text"
              name="title"
              id="title"
              class="form-control"
              required
            />
          </div>
          <div class="col-md-6 mb-3">
            <label for="description" class="form-label">Description</label>
            <input
              type="text"
              name="description"
              id="description"
              class="form-control"
            />
          </div>
        </div>
        <button type="submit" class="btn btn-success">Add Todo</button>
      </form>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-4">
      <div class="card text-center">
        <div class="card-body">
          <h5 class="card-title text-primary">Total</h5>
          <h2 class="text-primary">{{ total_count }}</h2>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card text-center">
        <div class="card-body">
          <h5 class="card-title text-warning">Pending</h5>
          <h2 class="text-warning">{{ pending_count }}</h2>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card text-center">
        <div class="card-body">
          <h5 class="card-title text-success">Completed</h5>
          <h2 class="text-success">{{ completed_count }}</h2>
        </div>
      </div>
    </div>
  </div>

  {% if todos %} {% for todo in todos %}
  <div class="card mb-3">
    <div class="card-body">
      <div class="row align-items-center">
        <div class="col-md-1">
          <form
            method="post"
            action="{% url 'toggle_todo' todo.id %}"
            style="display: inline"
          >
            {% csrf_token %}
            <button
              type="submit"
              class="btn btn-sm {% if todo.completed %}btn-success{% else %}btn-outline-success{% endif %}"
            >
              {% if todo.completed %}Done{% else %}Todo{% endif %}
            </button>
          </form>
        </div>
        <div class="col-md-7">
          <h5
            {%
            if
            todo.completed
            %}class="text-decoration-line-through text-muted"
            {%
            endif
            %}
          >
            {{ todo.title }}
          </h5>
          {% if todo.description %}
          <p class="text-muted mb-1">{{ todo.description }}</p>
          {% endif %}
          <small class="text-muted"
            >Created: {{ todo.created_at|date:"M d, Y H:i" }}</small
          >
        </div>
        <div class="col-md-4 text-end">
          <a
            href="{% url 'edit_todo' todo.id %}"
            class="btn btn-outline-primary btn-sm me-2"
            >Edit</a
          >
          <a
            href="{% url 'delete_todo' todo.id %}"
            class="btn btn-outline-danger btn-sm"
            >Delete</a
          >
        </div>
      </div>
    </div>
  </div>
  {% endfor %} {% else %}
  <div class="text-center py-5">
    <h3 class="text-muted">No todos yet!</h3>
    <p class="text-muted">Add your first todo using the form above.</p>
  </div>
  {% endif %}
</div>
{% endblock %}
