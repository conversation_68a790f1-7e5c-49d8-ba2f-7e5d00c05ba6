{% extends 'todo/base.html' %} {% block title %}Todo List - Todo App{% endblock
%} {% block content %}
<div class="row">
  <div class="col-12">
    <h1 class="text-center mb-4">
      <i class="fas fa-list-check text-primary"></i> My Todo List
    </h1>
  </div>
</div>

<!-- Add Todo Form -->
<div class="add-todo-form">
  <h3 class="mb-3"><i class="fas fa-plus text-success"></i> Add New Todo</h3>
  <form method="post" action="{% url 'add_todo' %}">
    {% csrf_token %}
    <div class="row">
      <div class="col-md-6 mb-3">
        <label for="title" class="form-label">Title *</label>
        <input
          type="text"
          class="form-control"
          id="title"
          name="title"
          required
        />
      </div>
      <div class="col-md-6 mb-3">
        <label for="description" class="form-label">Description</label>
        <input
          type="text"
          class="form-control"
          id="description"
          name="description"
        />
      </div>
    </div>
    <button type="submit" class="btn btn-success">
      <i class="fas fa-plus"></i> Add Todo
    </button>
  </form>
</div>

<!-- Todo Statistics -->
<div class="row mb-4">
  <div class="col-md-4">
    <div class="card stats-card text-center">
      <div class="card-body">
        <h5 class="card-title text-primary">Total</h5>
        <h2 class="text-primary">{{ todos.count }}</h2>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card stats-card text-center">
      <div class="card-body">
        <h5 class="card-title text-warning">Pending</h5>
        <h2 class="text-warning">
          {{ todos|length|add:"-"|add:todos.filter:completed=True|length }}
        </h2>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card stats-card text-center">
      <div class="card-body">
        <h5 class="card-title text-success">Completed</h5>
        <h2 class="text-success">
          {{ todos|length|add:"-"|add:todos.filter:completed=False|length }}
        </h2>
      </div>
    </div>
  </div>
</div>

<!-- Todo List -->
{% if todos %}
<div class="row">
  {% for todo in todos %}
  <div class="col-12">
    <div class="todo-item {% if todo.completed %}todo-completed{% endif %}">
      <div class="row align-items-center">
        <div class="col-md-1">
          <form
            method="post"
            action="{% url 'toggle_todo' todo.id %}"
            style="display: inline"
          >
            {% csrf_token %}
            <button
              type="submit"
              class="btn btn-circle {% if todo.completed %}btn-success{% else %}btn-outline-success{% endif %}"
            >
              {% if todo.completed %}
              <i class="fas fa-check"></i>
              {% else %}
              <i class="far fa-circle"></i>
              {% endif %}
            </button>
          </form>
        </div>
        <div class="col-md-7">
          <h5 class="mb-1 todo-title">{{ todo.title }}</h5>
          {% if todo.description %}
          <p class="text-muted mb-1">{{ todo.description }}</p>
          {% endif %}
          <small class="text-muted">
            Created: {{ todo.created_at|date:"M d, Y H:i" }} {% if
            todo.updated_at != todo.created_at %} | Updated: {{
            todo.updated_at|date:"M d, Y H:i" }} {% endif %}
          </small>
        </div>
        <div class="col-md-4 text-end">
          <a
            href="{% url 'edit_todo' todo.id %}"
            class="btn btn-outline-primary btn-sm me-2"
          >
            <i class="fas fa-edit"></i> Edit
          </a>
          <a
            href="{% url 'delete_todo' todo.id %}"
            class="btn btn-outline-danger btn-sm"
          >
            <i class="fas fa-trash"></i> Delete
          </a>
        </div>
      </div>
    </div>
  </div>
  {% endfor %}
</div>
{% else %}
<div class="text-center py-5">
  <i class="fas fa-clipboard-list fa-5x text-muted mb-3"></i>
  <h3 class="text-muted">No todos yet!</h3>
  <p class="text-muted">Add your first todo using the form above.</p>
</div>
{% endif %} {% endblock %}
