{% extends 'todo/base.html' %} {% block title %}Todo List{% endblock %} {% block
content %}
<h1>My Todo List</h1>

<form
  method="post"
  action="{% url 'add_todo' %}"
  onsubmit="console.log('Form submitted');"
>
  {% csrf_token %}
  <div class="mb-3">
    <label for="title">Title:</label>
    <input
      type="text"
      name="title"
      id="title"
      class="form-control"
      required
      placeholder="Enter todo title"
    />
  </div>
  <div class="mb-3">
    <label for="description">Description:</label>
    <input
      type="text"
      name="description"
      id="description"
      class="form-control"
      placeholder="Enter description (optional)"
    />
  </div>
  <button type="submit" class="btn btn-success">Add Todo</button>
</form>

<div class="mt-4">
  <h3>Statistics</h3>
  <p>
    Total: {{ total_count }} | Pending: {{ pending_count }} | Completed: {{
    completed_count }}
  </p>
</div>

{% if todos %}
<div class="mt-4">
  {% for todo in todos %}
  <div class="card mb-3">
    <div class="card-body">
      <h5>{{ todo.title }}</h5>
      {% if todo.description %}
      <p>{{ todo.description }}</p>
      {% endif %}
      <p><small>Created: {{ todo.created_at|date:"M d, Y H:i" }}</small></p>

      <form
        method="post"
        action="{% url 'toggle_todo' todo.id %}"
        style="display: inline"
      >
        {% csrf_token %}
        <button
          type="submit"
          class="btn btn-sm {% if todo.completed %}btn-success{% else %}btn-outline-success{% endif %}"
        >
          {% if todo.completed %}Completed{% else %}Mark Complete{% endif %}
        </button>
      </form>

      <a
        href="{% url 'edit_todo' todo.id %}"
        class="btn btn-sm btn-outline-primary"
        >Edit</a
      >
      <a
        href="{% url 'delete_todo' todo.id %}"
        class="btn btn-sm btn-outline-danger"
        >Delete</a
      >
    </div>
  </div>
  {% endfor %}
</div>
{% else %}
<div class="mt-4">
  <p>No todos yet! Add your first todo above.</p>
</div>
{% endif %} {% endblock %}
