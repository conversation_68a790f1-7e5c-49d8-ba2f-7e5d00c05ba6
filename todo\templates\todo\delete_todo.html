{% extends 'todo/base.html' %}

{% block title %}Delete Todo - Todo App{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h3 class="mb-0">
                    <i class="fas fa-trash"></i> Delete Todo
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                
                <p>Are you sure you want to delete this todo?</p>
                
                <div class="todo-preview p-3 bg-light rounded">
                    <h5>{{ todo.title }}</h5>
                    {% if todo.description %}
                        <p class="text-muted mb-1">{{ todo.description }}</p>
                    {% endif %}
                    <small class="text-muted">
                        Created: {{ todo.created_at|date:"M d, Y H:i" }}
                    </small>
                </div>
                
                <div class="d-flex justify-content-between mt-4">
                    <a href="{% url 'todo_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Cancel
                    </a>
                    <form method="post" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Delete Todo
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
