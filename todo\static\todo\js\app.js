// Todo App JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });

    // Add confirmation for delete actions
    const deleteLinks = document.querySelectorAll('a[href*="delete"]');
    deleteLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            if (!link.href.includes('delete_todo')) return;
            
            e.preventDefault();
            const todoTitle = link.closest('.todo-item').querySelector('h5').textContent;
            
            if (confirm(`Are you sure you want to delete "${todoTitle}"?`)) {
                window.location.href = link.href;
            }
        });
    });

    // Add smooth scrolling for form submission
    const addForm = document.querySelector('form[action*="add"]');
    if (addForm) {
        addForm.addEventListener('submit', function() {
            setTimeout(function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }, 100);
        });
    }

    // Add loading state to buttons
    const buttons = document.querySelectorAll('button[type="submit"]');
    buttons.forEach(function(button) {
        button.addEventListener('click', function() {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            button.disabled = true;
            
            // Re-enable after form submission
            setTimeout(function() {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        });
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + Enter to submit add form
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            const addForm = document.querySelector('form[action*="add"]');
            if (addForm && document.activeElement.closest('form') === addForm) {
                addForm.submit();
            }
        }
        
        // Escape to clear form
        if (e.key === 'Escape') {
            const titleInput = document.querySelector('#title');
            const descInput = document.querySelector('#description');
            if (titleInput) titleInput.value = '';
            if (descInput) descInput.value = '';
        }
    });

    // Add focus to title input on page load
    const titleInput = document.querySelector('#title');
    if (titleInput) {
        titleInput.focus();
    }

    // Add character counter for description
    const descInput = document.querySelector('#description');
    if (descInput) {
        const maxLength = 500;
        const counter = document.createElement('small');
        counter.className = 'text-muted';
        counter.textContent = `0/${maxLength} characters`;
        descInput.parentNode.appendChild(counter);
        
        descInput.addEventListener('input', function() {
            const length = this.value.length;
            counter.textContent = `${length}/${maxLength} characters`;
            
            if (length > maxLength * 0.9) {
                counter.className = 'text-warning';
            } else {
                counter.className = 'text-muted';
            }
        });
    }

    // Add animation to completed todos
    const toggleButtons = document.querySelectorAll('button[formaction*="toggle"]');
    toggleButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const todoItem = button.closest('.todo-item');
            todoItem.style.transition = 'all 0.5s ease';
        });
    });
});
