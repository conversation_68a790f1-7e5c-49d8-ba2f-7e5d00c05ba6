from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from .models import Todo

# Create your views here.

def todo_list(request):
    """Display all todos"""
    todos = Todo.objects.all()
    total_count = todos.count()
    completed_count = todos.filter(completed=True).count()
    pending_count = total_count - completed_count

    context = {
        'todos': todos,
        'total_count': total_count,
        'completed_count': completed_count,
        'pending_count': pending_count,
    }
    return render(request, 'todo/todo_list.html', context)

def add_todo(request):
    """Add a new todo"""
    print(f"Request method: {request.method}")
    if request.method == 'POST':
        title = request.POST.get('title')
        description = request.POST.get('description')
        print(f"Title: {title}, Description: {description}")

        if title:
            todo = Todo.objects.create(title=title, description=description)
            print(f"Created todo: {todo}")
            messages.success(request, 'Todo added successfully!')
        else:
            messages.error(request, 'Title is required!')

    return redirect('todo_list')

def toggle_todo(request, todo_id):
    """Toggle todo completion status"""
    todo = get_object_or_404(Todo, id=todo_id)
    todo.completed = not todo.completed
    todo.save()

    status = "completed" if todo.completed else "marked as incomplete"
    messages.success(request, f'Todo "{todo.title}" {status}!')
    return redirect('todo_list')

def edit_todo(request, todo_id):
    """Edit an existing todo"""
    todo = get_object_or_404(Todo, id=todo_id)

    if request.method == 'POST':
        title = request.POST.get('title')
        description = request.POST.get('description')
        completed = request.POST.get('completed') == 'on'

        if title:
            todo.title = title
            todo.description = description
            todo.completed = completed
            todo.save()
            messages.success(request, 'Todo updated successfully!')
            return redirect('todo_list')
        else:
            messages.error(request, 'Title is required!')

    return render(request, 'todo/edit_todo.html', {'todo': todo})

def delete_todo(request, todo_id):
    """Delete a todo"""
    todo = get_object_or_404(Todo, id=todo_id)

    if request.method == 'POST':
        todo_title = todo.title
        todo.delete()
        messages.success(request, f'Todo "{todo_title}" deleted successfully!')
        return redirect('todo_list')

    return render(request, 'todo/delete_todo.html', {'todo': todo})
